#ifndef MEMORY_MANAGER_H
#define MEMORY_MANAGER_H

#ifdef __cplusplus
extern "C" {
#endif

#include "system_types.h"
#include "object_base.h"

// 内存块头部结构
typedef struct memory_block {
    uint32_t magic;                     // 魔数
    uint32_t size;                      // 块大小
    bool is_free;                       // 是否空闲
    struct memory_block* next;          // 下一个块
    struct memory_block* prev;          // 前一个块
    const char* file;                   // 分配文件
    uint32_t line;                      // 分配行号
    system_time_t alloc_time;           // 分配时间
} memory_block_t;

// 内存池结构
typedef struct {
    base_object_t base;                 // 基础对象
    uint8_t* pool_start;                // 池起始地址
    uint8_t* pool_end;                  // 池结束地址
    uint32_t pool_size;                 // 池总大小
    uint32_t used_size;                 // 已使用大小
    uint32_t peak_size;                 // 峰值使用大小
    uint32_t block_count;               // 块数量
    uint32_t free_block_count;          // 空闲块数量
    memory_block_t* free_list;          // 空闲链表
    memory_block_t* used_list;          // 已用链表
    bool debug_enabled;                 // 调试模式
    uint32_t alloc_count;               // 分配次数
    uint32_t free_count;                // 释放次数
} memory_pool_t;

// 内存统计信息
typedef struct {
    uint32_t total_size;                // 总大小
    uint32_t used_size;                 // 已使用大小
    uint32_t free_size;                 // 空闲大小
    uint32_t peak_size;                 // 峰值大小
    uint32_t block_count;               // 总块数
    uint32_t free_block_count;          // 空闲块数
    uint32_t alloc_count;               // 分配次数
    uint32_t free_count;                // 释放次数
    uint32_t fragmentation_ratio;       // 碎片率(百分比)
} memory_stats_t;

// 内存池配置
typedef struct {
    uint8_t* pool_buffer;               // 池缓冲区
    uint32_t pool_size;                 // 池大小
    bool debug_enabled;                 // 调试模式
    uint32_t alignment;                 // 对齐大小
} memory_pool_config_t;

// 全局内存池
extern memory_pool_t* g_memory_pool;

/**
 * @brief 创建内存池
 * @param config 配置参数
 * @return 内存池对象
 */
memory_pool_t* memory_pool_create(const memory_pool_config_t* config);

/**
 * @brief 销毁内存池
 * @param pool 内存池对象
 */
void memory_pool_destroy(memory_pool_t* pool);

/**
 * @brief 初始化全局内存池
 * @param config 配置参数
 * @return 操作结果
 */
system_result_e memory_manager_init(const memory_pool_config_t* config);

/**
 * @brief 反初始化全局内存池
 */
void memory_manager_deinit(void);

/**
 * @brief 分配内存
 * @param pool 内存池（NULL使用全局池）
 * @param size 大小
 * @param file 文件名（调试用）
 * @param line 行号（调试用）
 * @return 分配的内存指针
 */
void* memory_pool_alloc_debug(memory_pool_t* pool, uint32_t size, const char* file, uint32_t line);

/**
 * @brief 释放内存
 * @param pool 内存池（NULL使用全局池）
 * @param ptr 内存指针
 * @param file 文件名（调试用）
 * @param line 行号（调试用）
 * @return 操作结果
 */
system_result_e memory_pool_free_debug(memory_pool_t* pool, void* ptr, const char* file, uint32_t line);

/**
 * @brief 重新分配内存
 * @param pool 内存池（NULL使用全局池）
 * @param ptr 原内存指针
 * @param new_size 新大小
 * @param file 文件名（调试用）
 * @param line 行号（调试用）
 * @return 新的内存指针
 */
void* memory_pool_realloc_debug(memory_pool_t* pool, void* ptr, uint32_t new_size, const char* file, uint32_t line);

/**
 * @brief 分配并清零内存
 * @param pool 内存池（NULL使用全局池）
 * @param count 元素数量
 * @param size 元素大小
 * @param file 文件名（调试用）
 * @param line 行号（调试用）
 * @return 分配的内存指针
 */
void* memory_pool_calloc_debug(memory_pool_t* pool, uint32_t count, uint32_t size, const char* file, uint32_t line);

/**
 * @brief 获取内存统计信息
 * @param pool 内存池（NULL使用全局池）
 * @param stats 统计信息输出
 * @return 操作结果
 */
system_result_e memory_pool_get_stats(memory_pool_t* pool, memory_stats_t* stats);

/**
 * @brief 检查内存完整性
 * @param pool 内存池（NULL使用全局池）
 * @return 操作结果
 */
system_result_e memory_pool_check_integrity(memory_pool_t* pool);

/**
 * @brief 内存碎片整理
 * @param pool 内存池（NULL使用全局池）
 * @return 操作结果
 */
system_result_e memory_pool_defragment(memory_pool_t* pool);

/**
 * @brief 打印内存使用信息（调试用）
 * @param pool 内存池（NULL使用全局池）
 */
void memory_pool_dump(memory_pool_t* pool);

/**
 * @brief 检查内存泄漏
 * @param pool 内存池（NULL使用全局池）
 * @return 泄漏的内存块数量
 */
uint32_t memory_pool_check_leaks(memory_pool_t* pool);

// 内存分配便利宏
#ifdef DEBUG
    #define sys_malloc(size) memory_pool_alloc_debug(NULL, (size), __FILE__, __LINE__)
    #define sys_free(ptr) memory_pool_free_debug(NULL, (ptr), __FILE__, __LINE__)
    #define sys_realloc(ptr, size) memory_pool_realloc_debug(NULL, (ptr), (size), __FILE__, __LINE__)
    #define sys_calloc(count, size) memory_pool_calloc_debug(NULL, (count), (size), __FILE__, __LINE__)
#else
    #define sys_malloc(size) memory_pool_alloc_debug(NULL, (size), NULL, 0)
    #define sys_free(ptr) memory_pool_free_debug(NULL, (ptr), NULL, 0)
    #define sys_realloc(ptr, size) memory_pool_realloc_debug(NULL, (ptr), (size), NULL, 0)
    #define sys_calloc(count, size) memory_pool_calloc_debug(NULL, (count), (size), NULL, 0)
#endif

// 内存池操作宏
#define pool_malloc(pool, size) memory_pool_alloc_debug((pool), (size), __FILE__, __LINE__)
#define pool_free(pool, ptr) memory_pool_free_debug((pool), (ptr), __FILE__, __LINE__)
#define pool_realloc(pool, ptr, size) memory_pool_realloc_debug((pool), (ptr), (size), __FILE__, __LINE__)
#define pool_calloc(pool, count, size) memory_pool_calloc_debug((pool), (count), (size), __FILE__, __LINE__)

// 安全内存操作宏
#define SAFE_FREE(ptr) \
    do { \
        if ((ptr) != NULL) { \
            sys_free(ptr); \
            (ptr) = NULL; \
        } \
    } while(0)

#define SAFE_POOL_FREE(pool, ptr) \
    do { \
        if ((ptr) != NULL) { \
            pool_free(pool, ptr); \
            (ptr) = NULL; \
        } \
    } while(0)

// 内存分配检查宏
#define CHECK_ALLOC(ptr) \
    do { \
        if ((ptr) == NULL) { \
            ERROR_LOG("Memory allocation failed"); \
            return SYS_NO_MEMORY; \
        } \
    } while(0)

#define CHECK_ALLOC_GOTO(ptr, label) \
    do { \
        if ((ptr) == NULL) { \
            ERROR_LOG("Memory allocation failed"); \
            goto label; \
        } \
    } while(0)

// 内存对齐分配宏
#define ALIGNED_MALLOC(size, align) \
    memory_pool_alloc_debug(NULL, ALIGN_SIZE(size, align), __FILE__, __LINE__)

// 结构体分配宏
#define STRUCT_MALLOC(type) ((type*)sys_malloc(sizeof(type)))
#define STRUCT_CALLOC(type) ((type*)sys_calloc(1, sizeof(type)))
#define ARRAY_MALLOC(type, count) ((type*)sys_malloc(sizeof(type) * (count)))
#define ARRAY_CALLOC(type, count) ((type*)sys_calloc(count, sizeof(type)))

// 字符串分配宏
#define STRING_MALLOC(len) ((char*)sys_malloc((len) + 1))
#define STRING_DUP(str) \
    ({ \
        const char* _src = (str); \
        char* _dst = NULL; \
        if (_src) { \
            uint32_t _len = strlen(_src); \
            _dst = STRING_MALLOC(_len); \
            if (_dst) strcpy(_dst, _src); \
        } \
        _dst; \
    })

#ifdef __cplusplus
}
#endif

#endif // MEMORY_MANAGER_H
