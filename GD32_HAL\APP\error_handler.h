#ifndef ERROR_HANDLER_H
#define ERROR_HANDLER_H

#ifdef __cplusplus
extern "C" {
#endif

#include "system_types.h"
#include "object_base.h"

// 错误处理器对象类型
DEFINE_DERIVED_OBJECT(error_handler, struct {
    system_log_func_t log_func;        // 日志函数
    system_callback_t error_callback;  // 错误回调
    log_level_e min_log_level;         // 最小日志级别
    uint32_t error_count;              // 错误计数
    uint32_t warning_count;            // 警告计数
    bool panic_on_critical;            // 严重错误时是否panic
    char last_error_msg[256];          // 最后错误消息
});

// 错误信息结构
typedef struct {
    system_result_e error_code;        // 错误码
    const char* file;                  // 文件名
    uint32_t line;                     // 行号
    const char* function;              // 函数名
    const char* message;               // 错误消息
    system_time_t timestamp;           // 时间戳
    void* context;                     // 上下文数据
} error_info_t;

// 错误处理策略
typedef enum {
    ERROR_STRATEGY_IGNORE = 0,         // 忽略错误
    ERROR_STRATEGY_LOG = 1,            // 记录日志
    ERROR_STRATEGY_CALLBACK = 2,       // 调用回调
    ERROR_STRATEGY_RETRY = 3,          // 重试操作
    ERROR_STRATEGY_FALLBACK = 4,       // 降级处理
    ERROR_STRATEGY_PANIC = 5,          // 系统panic
    ERROR_STRATEGY_MAX
} error_strategy_e;

// 错误处理配置
typedef struct {
    system_log_func_t log_func;        // 日志函数
    system_callback_t error_callback;  // 错误回调函数
    log_level_e min_log_level;         // 最小日志级别
    bool panic_on_critical;            // 严重错误时panic
    error_strategy_e default_strategy; // 默认处理策略
} error_handler_config_t;

// 全局错误处理器实例
extern error_handler_t* g_error_handler;

/**
 * @brief 创建错误处理器
 * @param config 配置参数
 * @return 错误处理器对象
 */
error_handler_t* error_handler_create(const error_handler_config_t* config);

/**
 * @brief 销毁错误处理器
 * @param handler 错误处理器对象
 */
void error_handler_destroy(error_handler_t* handler);

/**
 * @brief 初始化全局错误处理器
 * @param config 配置参数
 * @return 操作结果
 */
system_result_e error_handler_global_init(const error_handler_config_t* config);

/**
 * @brief 反初始化全局错误处理器
 */
void error_handler_global_deinit(void);

/**
 * @brief 处理错误
 * @param handler 错误处理器（NULL使用全局处理器）
 * @param error_info 错误信息
 * @param strategy 处理策略
 * @return 操作结果
 */
system_result_e error_handler_handle_error(error_handler_t* handler,
                                           const error_info_t* error_info,
                                           error_strategy_e strategy);

/**
 * @brief 记录错误日志
 * @param handler 错误处理器（NULL使用全局处理器）
 * @param level 日志级别
 * @param file 文件名
 * @param line 行号
 * @param func 函数名
 * @param format 格式字符串
 * @param ... 参数
 */
void error_handler_log(error_handler_t* handler,
                      log_level_e level,
                      const char* file,
                      uint32_t line,
                      const char* func,
                      const char* format, ...);

/**
 * @brief 获取错误统计信息
 * @param handler 错误处理器（NULL使用全局处理器）
 * @param error_count 错误计数输出
 * @param warning_count 警告计数输出
 * @return 操作结果
 */
system_result_e error_handler_get_stats(error_handler_t* handler,
                                        uint32_t* error_count,
                                        uint32_t* warning_count);

/**
 * @brief 重置错误统计
 * @param handler 错误处理器（NULL使用全局处理器）
 * @return 操作结果
 */
system_result_e error_handler_reset_stats(error_handler_t* handler);

/**
 * @brief 获取最后错误消息
 * @param handler 错误处理器（NULL使用全局处理器）
 * @return 错误消息字符串
 */
const char* error_handler_get_last_error(error_handler_t* handler);

/**
 * @brief 系统断言失败处理
 * @param file 文件名
 * @param line 行号
 * @param expr 断言表达式
 */
void system_assert_failed(const char* file, uint32_t line, const char* expr);

/**
 * @brief 系统panic处理
 * @param message panic消息
 */
void system_panic(const char* message);

// 错误处理便利宏

// 错误日志宏
#define ERROR_LOG(format, ...) \
    error_handler_log(NULL, LOG_LEVEL_ERROR, __FILE__, __LINE__, __FUNCTION__, format, ##__VA_ARGS__)

#define WARN_LOG(format, ...) \
    error_handler_log(NULL, LOG_LEVEL_WARN, __FILE__, __LINE__, __FUNCTION__, format, ##__VA_ARGS__)

#define INFO_LOG(format, ...) \
    error_handler_log(NULL, LOG_LEVEL_INFO, __FILE__, __LINE__, __FUNCTION__, format, ##__VA_ARGS__)

#define DEBUG_LOG(format, ...) \
    error_handler_log(NULL, LOG_LEVEL_DEBUG, __FILE__, __LINE__, __FUNCTION__, format, ##__VA_ARGS__)

// 错误检查宏
#define CHECK_RESULT(expr) \
    do { \
        system_result_e _result = (expr); \
        if (_result != SYS_OK) { \
            ERROR_LOG("Operation failed: %s, result: %d", #expr, _result); \
            return _result; \
        } \
    } while(0)

#define CHECK_RESULT_GOTO(expr, label) \
    do { \
        system_result_e _result = (expr); \
        if (_result != SYS_OK) { \
            ERROR_LOG("Operation failed: %s, result: %d", #expr, _result); \
            goto label; \
        } \
    } while(0)

#define CHECK_NULL_RETURN(ptr, ret_val) \
    do { \
        if ((ptr) == NULL) { \
            ERROR_LOG("Null pointer: %s", #ptr); \
            return (ret_val); \
        } \
    } while(0)

#define CHECK_NULL_GOTO(ptr, label) \
    do { \
        if ((ptr) == NULL) { \
            ERROR_LOG("Null pointer: %s", #ptr); \
            goto label; \
        } \
    } while(0)

// 参数验证宏
#define VALIDATE_PARAM(expr) \
    do { \
        if (!(expr)) { \
            ERROR_LOG("Invalid parameter: %s", #expr); \
            return SYS_INVALID_PARAM; \
        } \
    } while(0)

#define VALIDATE_PARAM_GOTO(expr, label) \
    do { \
        if (!(expr)) { \
            ERROR_LOG("Invalid parameter: %s", #expr); \
            goto label; \
        } \
    } while(0)

// 错误处理宏
#define HANDLE_ERROR(error_code, message) \
    do { \
        error_info_t _error_info = { \
            .error_code = (error_code), \
            .file = __FILE__, \
            .line = __LINE__, \
            .function = __FUNCTION__, \
            .message = (message), \
            .timestamp = 0, /* 将在处理函数中设置 */ \
            .context = NULL \
        }; \
        error_handler_handle_error(NULL, &_error_info, ERROR_STRATEGY_LOG); \
    } while(0)

#define HANDLE_ERROR_WITH_STRATEGY(error_code, message, strategy) \
    do { \
        error_info_t _error_info = { \
            .error_code = (error_code), \
            .file = __FILE__, \
            .line = __LINE__, \
            .function = __FUNCTION__, \
            .message = (message), \
            .timestamp = 0, \
            .context = NULL \
        }; \
        error_handler_handle_error(NULL, &_error_info, (strategy)); \
    } while(0)

// 条件错误处理宏
#define ERROR_IF(condition, error_code, message) \
    do { \
        if (condition) { \
            HANDLE_ERROR(error_code, message); \
            return (error_code); \
        } \
    } while(0)

#define ERROR_IF_GOTO(condition, error_code, message, label) \
    do { \
        if (condition) { \
            HANDLE_ERROR(error_code, message); \
            goto label; \
        } \
    } while(0)

// 系统错误码转字符串函数
const char* system_result_to_string(system_result_e result);

#ifdef __cplusplus
}
#endif

#endif // ERROR_HANDLER_H
