#ifndef SYSTEM_TYPES_H
#define SYSTEM_TYPES_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>

// 系统魔数定义，用于对象类型验证
#define SYSTEM_MAGIC_BASE           0x53594D00  // "SYM" + 0x00
#define OBJECT_MAGIC_MASK           0xFFFFFF00
#define OBJECT_TYPE_MASK            0x000000FF

// 对象类型枚举
typedef enum {
    OBJ_TYPE_UNKNOWN = 0x00,
    OBJ_TYPE_BASE = 0x01,
    OBJ_TYPE_SAMPLING_FSM = 0x02,
    OBJ_TYPE_DATA_SUBJECT = 0x03,
    OBJ_TYPE_DATA_OBSERVER = 0x04,
    OBJ_TYPE_CONFIG_FACTORY = 0x05,
    OBJ_TYPE_CONFIG_BUILDER = 0x06,
    OBJ_TYPE_TASK_HANDLER = 0x07,
    OBJ_TYPE_TASK_SCHEDULER = 0x08,
    OBJ_TYPE_MEMORY_POOL = 0x09,
    OBJ_TYPE_ERROR_HANDLER = 0x0A,
    OBJ_TYPE_MAX = 0xFF
} object_type_e;

// 系统返回值类型
typedef enum {
    SYS_OK = 0,                     // 操作成功
    SYS_ERROR = 1,                  // 通用错误
    SYS_INVALID_PARAM = 2,          // 无效参数
    SYS_NO_MEMORY = 3,              // 内存不足
    SYS_TIMEOUT = 4,                // 操作超时
    SYS_BUSY = 5,                   // 系统忙碌
    SYS_NOT_SUPPORTED = 6,          // 不支持的操作
    SYS_NOT_INITIALIZED = 7,        // 未初始化
    SYS_ALREADY_INITIALIZED = 8,    // 已经初始化
    SYS_INVALID_STATE = 9,          // 无效状态
    SYS_BUFFER_FULL = 10,           // 缓冲区满
    SYS_BUFFER_EMPTY = 11,          // 缓冲区空
    SYS_CRC_ERROR = 12,             // CRC校验错误
    SYS_IO_ERROR = 13,              // IO错误
    SYS_PERMISSION_DENIED = 14,     // 权限拒绝
    SYS_RESOURCE_UNAVAILABLE = 15,  // 资源不可用
    SYS_RESULT_MAX = 0xFF
} system_result_e;

// 系统优先级定义
typedef enum {
    PRIORITY_LOWEST = 0,
    PRIORITY_LOW = 1,
    PRIORITY_NORMAL = 2,
    PRIORITY_HIGH = 3,
    PRIORITY_HIGHEST = 4,
    PRIORITY_CRITICAL = 5,
    PRIORITY_MAX = 0xFF
} system_priority_e;

// 系统状态定义
typedef enum {
    STATE_UNKNOWN = 0,
    STATE_INITIALIZING = 1,
    STATE_READY = 2,
    STATE_RUNNING = 3,
    STATE_PAUSED = 4,
    STATE_ERROR = 5,
    STATE_SHUTDOWN = 6,
    STATE_MAX = 0xFF
} system_state_e;

// 通用回调函数类型定义
typedef void (*system_callback_t)(void* context, void* data);
typedef system_result_e (*system_init_func_t)(void* context);
typedef system_result_e (*system_deinit_func_t)(void* context);
typedef system_result_e (*system_process_func_t)(void* context, void* data);
typedef system_result_e (*system_validate_func_t)(const void* data);

// 事件类型定义
typedef enum {
    EVENT_NONE = 0,
    EVENT_INIT = 1,
    EVENT_START = 2,
    EVENT_STOP = 3,
    EVENT_PAUSE = 4,
    EVENT_RESUME = 5,
    EVENT_ERROR = 6,
    EVENT_TIMEOUT = 7,
    EVENT_DATA_READY = 8,
    EVENT_CONFIG_CHANGED = 9,
    EVENT_STATE_CHANGED = 10,
    EVENT_USER_DEFINED = 100,
    EVENT_MAX = 0xFFFF
} system_event_e;

// 事件数据结构
typedef struct {
    system_event_e event_type;      // 事件类型
    uint32_t timestamp;             // 时间戳
    void* source;                   // 事件源
    void* data;                     // 事件数据
    uint32_t data_size;             // 数据大小
    system_priority_e priority;     // 事件优先级
} system_event_t;

// 系统配置结构
typedef struct {
    uint32_t magic;                 // 魔数
    uint32_t version;               // 版本号
    uint32_t max_objects;           // 最大对象数量
    uint32_t memory_pool_size;      // 内存池大小
    uint32_t event_queue_size;      // 事件队列大小
    bool debug_enabled;             // 调试模式
    system_priority_e log_level;    // 日志级别
} system_config_t;

// 系统统计信息
typedef struct {
    uint32_t total_objects;         // 总对象数
    uint32_t active_objects;        // 活跃对象数
    uint32_t memory_used;           // 已使用内存
    uint32_t memory_peak;           // 内存使用峰值
    uint32_t events_processed;      // 已处理事件数
    uint32_t errors_count;          // 错误计数
    uint32_t uptime_seconds;        // 运行时间(秒)
} system_stats_t;

// 通用链表节点
typedef struct list_node {
    struct list_node* next;         // 下一个节点
    struct list_node* prev;         // 前一个节点
    void* data;                     // 节点数据
} list_node_t;

// 通用链表
typedef struct {
    list_node_t* head;              // 头节点
    list_node_t* tail;              // 尾节点
    uint32_t count;                 // 节点数量
} list_t;

// 通用队列
typedef struct {
    void** items;                   // 队列项数组
    uint32_t capacity;              // 队列容量
    uint32_t count;                 // 当前项数
    uint32_t head;                  // 队列头索引
    uint32_t tail;                  // 队列尾索引
} queue_t;

// 通用栈
typedef struct {
    void** items;                   // 栈项数组
    uint32_t capacity;              // 栈容量
    uint32_t count;                 // 当前项数
} stack_t;

// 系统时间类型
typedef uint32_t system_time_t;

// 获取系统时间的函数指针类型
typedef system_time_t (*get_system_time_func_t)(void);

// 延时函数指针类型
typedef void (*system_delay_func_t)(uint32_t ms);

// 系统断言宏
#ifdef DEBUG
    #define SYSTEM_ASSERT(expr) \
        do { \
            if (!(expr)) { \
                system_assert_failed(__FILE__, __LINE__, #expr); \
            } \
        } while(0)
#else
    #define SYSTEM_ASSERT(expr) ((void)0)
#endif

// 系统日志级别
typedef enum {
    LOG_LEVEL_NONE = 0,
    LOG_LEVEL_ERROR = 1,
    LOG_LEVEL_WARN = 2,
    LOG_LEVEL_INFO = 3,
    LOG_LEVEL_DEBUG = 4,
    LOG_LEVEL_VERBOSE = 5
} log_level_e;

// 日志函数指针类型
typedef void (*system_log_func_t)(log_level_e level, const char* tag, const char* format, ...);

// 内存对齐宏
#define ALIGN_SIZE(size, align) (((size) + (align) - 1) & ~((align) - 1))
#define ALIGN_PTR(ptr, align) ((void*)(((uintptr_t)(ptr) + (align) - 1) & ~((align) - 1)))

// 数组大小宏
#define ARRAY_SIZE(arr) (sizeof(arr) / sizeof((arr)[0]))

// 最小值和最大值宏
#define MIN(a, b) ((a) < (b) ? (a) : (b))
#define MAX(a, b) ((a) > (b) ? (a) : (b))

// 位操作宏
#define SET_BIT(reg, bit)     ((reg) |= (1U << (bit)))
#define CLEAR_BIT(reg, bit)   ((reg) &= ~(1U << (bit)))
#define TOGGLE_BIT(reg, bit)  ((reg) ^= (1U << (bit)))
#define CHECK_BIT(reg, bit)   (((reg) >> (bit)) & 1U)

// 字节序转换宏（假设小端序系统）
#define SWAP16(x) ((((x) & 0xFF) << 8) | (((x) >> 8) & 0xFF))
#define SWAP32(x) ((((x) & 0xFF) << 24) | (((x) & 0xFF00) << 8) | \
                   (((x) >> 8) & 0xFF00) | (((x) >> 24) & 0xFF))

// 系统断言失败处理函数声明
void system_assert_failed(const char* file, uint32_t line, const char* expr);

#ifdef __cplusplus
}
#endif

#endif // SYSTEM_TYPES_H
