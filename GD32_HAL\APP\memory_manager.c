#include "memory_manager.h"
#include "error_handler.h"
#include <string.h>

// 全局内存池实例
memory_pool_t* g_memory_pool = NULL;

// 内存块魔数
#define MEMORY_BLOCK_MAGIC      0x4D454D42  // "MEMB"
#define MEMORY_BLOCK_FREE_MAGIC 0x46524545  // "FREE"

// 最小块大小（包含头部）
#define MIN_BLOCK_SIZE (sizeof(memory_block_t) + 8)

// 内存对齐大小
#define DEFAULT_ALIGNMENT 8

// 获取系统时间的弱函数声明
extern system_time_t get_system_time(void);

// 内存池虚函数表
static void memory_pool_destroy_impl(base_object_t* self);
static object_type_e memory_pool_get_type_impl(const base_object_t* self);
static uint32_t memory_pool_get_size_impl(const base_object_t* self);

static const object_vtable_t memory_pool_vtable = {
    .destroy = memory_pool_destroy_impl,
    .get_type = memory_pool_get_type_impl,
    .get_size = memory_pool_get_size_impl,
    .init = NULL,
    .deinit = NULL,
    .clone = NULL,
    .compare = NULL,
    .serialize = NULL,
    .deserialize = NULL,
    .to_string = NULL
};

// 内部辅助函数
static memory_block_t* find_free_block(memory_pool_t* pool, uint32_t size);
static void split_block(memory_block_t* block, uint32_t size);
static void merge_free_blocks(memory_pool_t* pool);
static void add_to_free_list(memory_pool_t* pool, memory_block_t* block);
static void remove_from_free_list(memory_pool_t* pool, memory_block_t* block);
static void add_to_used_list(memory_pool_t* pool, memory_block_t* block);
static void remove_from_used_list(memory_pool_t* pool, memory_block_t* block);

// 虚函数实现
static void memory_pool_destroy_impl(base_object_t* self) {
    if (self) {
        memory_pool_t* pool = (memory_pool_t*)self;
        
        // 检查内存泄漏
        if (pool->debug_enabled) {
            uint32_t leaks = memory_pool_check_leaks(pool);
            if (leaks > 0) {
                ERROR_LOG("Memory pool destroyed with %d leaked blocks", leaks);
            }
        }
        
        // 不释放pool_start，因为它是外部提供的缓冲区
        // 只释放pool对象本身
        if (pool != g_memory_pool) {
            // 使用标准库free，因为这是在销毁内存池
            free(pool);
        }
    }
}

static object_type_e memory_pool_get_type_impl(const base_object_t* self) {
    (void)self;
    return OBJ_TYPE_MEMORY_POOL;
}

static uint32_t memory_pool_get_size_impl(const base_object_t* self) {
    (void)self;
    return sizeof(memory_pool_t);
}

// 查找合适的空闲块
static memory_block_t* find_free_block(memory_pool_t* pool, uint32_t size) {
    memory_block_t* current = pool->free_list;
    memory_block_t* best_fit = NULL;
    uint32_t best_size = UINT32_MAX;
    
    // 首次适应算法，寻找最小的合适块
    while (current) {
        if (current->is_free && current->size >= size) {
            if (current->size < best_size) {
                best_fit = current;
                best_size = current->size;
                
                // 如果找到完全匹配的块，直接返回
                if (current->size == size) {
                    break;
                }
            }
        }
        current = current->next;
    }
    
    return best_fit;
}

// 分割内存块
static void split_block(memory_block_t* block, uint32_t size) {
    if (!block || block->size <= size + MIN_BLOCK_SIZE) {
        return; // 不需要分割或剩余空间太小
    }
    
    // 创建新的空闲块
    memory_block_t* new_block = (memory_block_t*)((uint8_t*)block + sizeof(memory_block_t) + size);
    new_block->magic = MEMORY_BLOCK_FREE_MAGIC;
    new_block->size = block->size - size - sizeof(memory_block_t);
    new_block->is_free = true;
    new_block->next = block->next;
    new_block->prev = block;
    new_block->file = NULL;
    new_block->line = 0;
    new_block->alloc_time = 0;
    
    // 更新原块
    block->size = size;
    block->next = new_block;
    
    // 更新链表
    if (new_block->next) {
        new_block->next->prev = new_block;
    }
}

// 合并相邻的空闲块
static void merge_free_blocks(memory_pool_t* pool) {
    memory_block_t* current = pool->free_list;
    
    while (current && current->next) {
        if (current->is_free && current->next->is_free) {
            // 检查是否相邻
            uint8_t* current_end = (uint8_t*)current + sizeof(memory_block_t) + current->size;
            if (current_end == (uint8_t*)current->next) {
                // 合并块
                memory_block_t* next_block = current->next;
                current->size += sizeof(memory_block_t) + next_block->size;
                current->next = next_block->next;
                
                if (next_block->next) {
                    next_block->next->prev = current;
                }
                
                // 清除被合并块的魔数
                next_block->magic = 0;
                continue; // 继续检查当前块
            }
        }
        current = current->next;
    }
}

// 添加到空闲链表
static void add_to_free_list(memory_pool_t* pool, memory_block_t* block) {
    block->next = pool->free_list;
    block->prev = NULL;
    
    if (pool->free_list) {
        pool->free_list->prev = block;
    }
    
    pool->free_list = block;
    pool->free_block_count++;
}

// 从空闲链表移除
static void remove_from_free_list(memory_pool_t* pool, memory_block_t* block) {
    if (block->prev) {
        block->prev->next = block->next;
    } else {
        pool->free_list = block->next;
    }
    
    if (block->next) {
        block->next->prev = block->prev;
    }
    
    pool->free_block_count--;
}

// 添加到已用链表
static void add_to_used_list(memory_pool_t* pool, memory_block_t* block) {
    block->next = pool->used_list;
    block->prev = NULL;
    
    if (pool->used_list) {
        pool->used_list->prev = block;
    }
    
    pool->used_list = block;
}

// 从已用链表移除
static void remove_from_used_list(memory_pool_t* pool, memory_block_t* block) {
    if (block->prev) {
        block->prev->next = block->next;
    } else {
        pool->used_list = block->next;
    }
    
    if (block->next) {
        block->next->prev = block->prev;
    }
}

// 公共函数实现
memory_pool_t* memory_pool_create(const memory_pool_config_t* config) {
    if (!config || !config->pool_buffer || config->pool_size < MIN_BLOCK_SIZE * 2) {
        return NULL;
    }
    
    // 分配内存池对象（使用标准库malloc，因为内存池还未创建）
    memory_pool_t* pool = (memory_pool_t*)malloc(sizeof(memory_pool_t));
    if (!pool) {
        return NULL;
    }
    
    // 初始化基础对象
    pool->base.magic = SYSTEM_MAGIC_BASE | OBJ_TYPE_MEMORY_POOL;
    pool->base.ref_count = 1;
    pool->base.state = STATE_INITIALIZING;
    pool->base.vtable = &memory_pool_vtable;
    pool->base.private_data = NULL;
    pool->base.created_time = get_system_time();
    pool->base.last_access_time = pool->base.created_time;
    
    // 初始化内存池
    pool->pool_start = config->pool_buffer;
    pool->pool_end = config->pool_buffer + config->pool_size;
    pool->pool_size = config->pool_size;
    pool->used_size = 0;
    pool->peak_size = 0;
    pool->block_count = 1;
    pool->free_block_count = 1;
    pool->free_list = NULL;
    pool->used_list = NULL;
    pool->debug_enabled = config->debug_enabled;
    pool->alloc_count = 0;
    pool->free_count = 0;
    
    // 创建初始空闲块
    memory_block_t* initial_block = (memory_block_t*)pool->pool_start;
    initial_block->magic = MEMORY_BLOCK_FREE_MAGIC;
    initial_block->size = config->pool_size - sizeof(memory_block_t);
    initial_block->is_free = true;
    initial_block->next = NULL;
    initial_block->prev = NULL;
    initial_block->file = NULL;
    initial_block->line = 0;
    initial_block->alloc_time = 0;
    
    add_to_free_list(pool, initial_block);
    
    pool->base.state = STATE_READY;
    return pool;
}

void memory_pool_destroy(memory_pool_t* pool) {
    if (pool) {
        base_object_destroy(&pool->base);
    }
}

system_result_e memory_manager_init(const memory_pool_config_t* config) {
    if (g_memory_pool) {
        return SYS_ALREADY_INITIALIZED;
    }
    
    g_memory_pool = memory_pool_create(config);
    return g_memory_pool ? SYS_OK : SYS_ERROR;
}

void memory_manager_deinit(void) {
    if (g_memory_pool) {
        memory_pool_destroy(g_memory_pool);
        g_memory_pool = NULL;
    }
}

void* memory_pool_alloc_debug(memory_pool_t* pool, uint32_t size, const char* file, uint32_t line) {
    if (!pool) {
        pool = g_memory_pool;
    }
    
    if (!pool || size == 0) {
        return NULL;
    }
    
    // 对齐大小
    size = ALIGN_SIZE(size, DEFAULT_ALIGNMENT);
    
    // 查找合适的空闲块
    memory_block_t* block = find_free_block(pool, size);
    if (!block) {
        return NULL; // 内存不足
    }
    
    // 从空闲链表移除
    remove_from_free_list(pool, block);
    
    // 分割块（如果需要）
    split_block(block, size);
    
    // 标记为已使用
    block->magic = MEMORY_BLOCK_MAGIC;
    block->is_free = false;
    block->file = file;
    block->line = line;
    block->alloc_time = get_system_time();
    
    // 添加到已用链表
    add_to_used_list(pool, block);
    
    // 更新统计信息
    pool->used_size += block->size;
    if (pool->used_size > pool->peak_size) {
        pool->peak_size = pool->used_size;
    }
    pool->alloc_count++;
    
    // 返回用户数据指针
    return (uint8_t*)block + sizeof(memory_block_t);
}

system_result_e memory_pool_free_debug(memory_pool_t* pool, void* ptr, const char* file, uint32_t line) {
    if (!ptr) {
        return SYS_OK; // 允许释放NULL指针
    }
    
    if (!pool) {
        pool = g_memory_pool;
    }
    
    if (!pool) {
        return SYS_NOT_INITIALIZED;
    }
    
    // 获取内存块头部
    memory_block_t* block = (memory_block_t*)((uint8_t*)ptr - sizeof(memory_block_t));
    
    // 验证魔数
    if (block->magic != MEMORY_BLOCK_MAGIC) {
        ERROR_LOG("Invalid memory block magic at %s:%d", file ? file : "unknown", line);
        return SYS_ERROR;
    }
    
    // 验证地址范围
    if ((uint8_t*)block < pool->pool_start || (uint8_t*)block >= pool->pool_end) {
        ERROR_LOG("Memory block out of pool range at %s:%d", file ? file : "unknown", line);
        return SYS_ERROR;
    }
    
    // 从已用链表移除
    remove_from_used_list(pool, block);
    
    // 标记为空闲
    block->magic = MEMORY_BLOCK_FREE_MAGIC;
    block->is_free = true;
    
    // 添加到空闲链表
    add_to_free_list(pool, block);
    
    // 更新统计信息
    pool->used_size -= block->size;
    pool->free_count++;
    
    // 合并相邻空闲块
    merge_free_blocks(pool);
    
    return SYS_OK;
}

void* memory_pool_realloc_debug(memory_pool_t* pool, void* ptr, uint32_t new_size, const char* file, uint32_t line) {
    if (!ptr) {
        return memory_pool_alloc_debug(pool, new_size, file, line);
    }

    if (new_size == 0) {
        memory_pool_free_debug(pool, ptr, file, line);
        return NULL;
    }

    if (!pool) {
        pool = g_memory_pool;
    }

    if (!pool) {
        return NULL;
    }

    // 获取原块信息
    memory_block_t* old_block = (memory_block_t*)((uint8_t*)ptr - sizeof(memory_block_t));
    if (old_block->magic != MEMORY_BLOCK_MAGIC) {
        return NULL;
    }

    uint32_t old_size = old_block->size;
    new_size = ALIGN_SIZE(new_size, DEFAULT_ALIGNMENT);

    // 如果新大小相同，直接返回
    if (new_size == old_size) {
        return ptr;
    }

    // 分配新内存
    void* new_ptr = memory_pool_alloc_debug(pool, new_size, file, line);
    if (!new_ptr) {
        return NULL;
    }

    // 复制数据
    uint32_t copy_size = (new_size < old_size) ? new_size : old_size;
    memcpy(new_ptr, ptr, copy_size);

    // 释放原内存
    memory_pool_free_debug(pool, ptr, file, line);

    return new_ptr;
}

void* memory_pool_calloc_debug(memory_pool_t* pool, uint32_t count, uint32_t size, const char* file, uint32_t line) {
    uint32_t total_size = count * size;
    void* ptr = memory_pool_alloc_debug(pool, total_size, file, line);

    if (ptr) {
        memset(ptr, 0, total_size);
    }

    return ptr;
}

system_result_e memory_pool_get_stats(memory_pool_t* pool, memory_stats_t* stats) {
    if (!stats) {
        return SYS_INVALID_PARAM;
    }

    if (!pool) {
        pool = g_memory_pool;
    }

    if (!pool) {
        return SYS_NOT_INITIALIZED;
    }

    stats->total_size = pool->pool_size;
    stats->used_size = pool->used_size;
    stats->free_size = pool->pool_size - pool->used_size;
    stats->peak_size = pool->peak_size;
    stats->block_count = pool->block_count;
    stats->free_block_count = pool->free_block_count;
    stats->alloc_count = pool->alloc_count;
    stats->free_count = pool->free_count;

    // 计算碎片率
    if (pool->pool_size > 0) {
        stats->fragmentation_ratio = (pool->free_block_count * 100) / pool->block_count;
    } else {
        stats->fragmentation_ratio = 0;
    }

    return SYS_OK;
}

system_result_e memory_pool_check_integrity(memory_pool_t* pool) {
    if (!pool) {
        pool = g_memory_pool;
    }

    if (!pool) {
        return SYS_NOT_INITIALIZED;
    }

    // 检查空闲链表
    memory_block_t* current = pool->free_list;
    uint32_t free_count = 0;

    while (current) {
        if (current->magic != MEMORY_BLOCK_FREE_MAGIC) {
            ERROR_LOG("Corrupted free block magic: 0x%08X", current->magic);
            return SYS_ERROR;
        }

        if (!current->is_free) {
            ERROR_LOG("Non-free block in free list");
            return SYS_ERROR;
        }

        free_count++;
        current = current->next;
    }

    // 检查已用链表
    current = pool->used_list;
    uint32_t used_count = 0;

    while (current) {
        if (current->magic != MEMORY_BLOCK_MAGIC) {
            ERROR_LOG("Corrupted used block magic: 0x%08X", current->magic);
            return SYS_ERROR;
        }

        if (current->is_free) {
            ERROR_LOG("Free block in used list");
            return SYS_ERROR;
        }

        used_count++;
        current = current->next;
    }

    // 验证计数
    if (free_count != pool->free_block_count) {
        ERROR_LOG("Free block count mismatch: %d vs %d", free_count, pool->free_block_count);
        return SYS_ERROR;
    }

    return SYS_OK;
}

system_result_e memory_pool_defragment(memory_pool_t* pool) {
    if (!pool) {
        pool = g_memory_pool;
    }

    if (!pool) {
        return SYS_NOT_INITIALIZED;
    }

    // 简单的碎片整理：合并相邻空闲块
    merge_free_blocks(pool);

    return SYS_OK;
}

void memory_pool_dump(memory_pool_t* pool) {
    if (!pool) {
        pool = g_memory_pool;
    }

    if (!pool) {
        ERROR_LOG("Memory pool not initialized");
        return;
    }

    INFO_LOG("=== Memory Pool Dump ===");
    INFO_LOG("Pool: %p - %p (%d bytes)", pool->pool_start, pool->pool_end, pool->pool_size);
    INFO_LOG("Used: %d bytes, Peak: %d bytes", pool->used_size, pool->peak_size);
    INFO_LOG("Blocks: %d total, %d free", pool->block_count, pool->free_block_count);
    INFO_LOG("Operations: %d allocs, %d frees", pool->alloc_count, pool->free_count);

    if (pool->debug_enabled) {
        INFO_LOG("=== Used Blocks ===");
        memory_block_t* current = pool->used_list;
        int index = 0;

        while (current && index < 10) { // 限制输出数量
            INFO_LOG("Block %d: %p, size=%d, file=%s:%d",
                index, current, current->size,
                current->file ? current->file : "unknown",
                current->line);
            current = current->next;
            index++;
        }

        if (current) {
            INFO_LOG("... and more blocks");
        }
    }
}

uint32_t memory_pool_check_leaks(memory_pool_t* pool) {
    if (!pool) {
        pool = g_memory_pool;
    }

    if (!pool) {
        return 0;
    }

    uint32_t leak_count = 0;
    memory_block_t* current = pool->used_list;

    while (current) {
        leak_count++;
        if (pool->debug_enabled) {
            WARN_LOG("Memory leak: %d bytes allocated at %s:%d",
                current->size,
                current->file ? current->file : "unknown",
                current->line);
        }
        current = current->next;
    }

    return leak_count;
}
