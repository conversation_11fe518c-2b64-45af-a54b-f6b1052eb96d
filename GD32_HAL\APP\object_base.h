#ifndef OBJECT_BASE_H
#define OBJECT_BASE_H

#ifdef __cplusplus
extern "C" {
#endif

#include "system_types.h"

// 前向声明
typedef struct base_object base_object_t;

// 基础对象虚函数表
typedef struct {
    // 对象销毁函数
    void (*destroy)(base_object_t* self);
    
    // 获取对象类型
    object_type_e (*get_type)(const base_object_t* self);
    
    // 获取对象大小
    uint32_t (*get_size)(const base_object_t* self);
    
    // 对象初始化
    system_result_e (*init)(base_object_t* self, void* config);
    
    // 对象反初始化
    system_result_e (*deinit)(base_object_t* self);
    
    // 对象克隆
    base_object_t* (*clone)(const base_object_t* self);
    
    // 对象比较
    int (*compare)(const base_object_t* self, const base_object_t* other);
    
    // 对象序列化
    system_result_e (*serialize)(const base_object_t* self, uint8_t* buffer, uint32_t* size);
    
    // 对象反序列化
    system_result_e (*deserialize)(base_object_t* self, const uint8_t* buffer, uint32_t size);
    
    // 对象转字符串（调试用）
    const char* (*to_string)(const base_object_t* self);
} object_vtable_t;

// 基础对象结构
struct base_object {
    uint32_t magic;                 // 魔数，包含类型信息
    uint32_t ref_count;             // 引用计数
    system_state_e state;           // 对象状态
    const object_vtable_t* vtable;  // 虚函数表指针
    void* private_data;             // 私有数据指针
    system_time_t created_time;     // 创建时间
    system_time_t last_access_time; // 最后访问时间
};

// 对象创建配置
typedef struct {
    object_type_e type;             // 对象类型
    uint32_t private_data_size;     // 私有数据大小
    const object_vtable_t* vtable;  // 虚函数表
    void* init_config;              // 初始化配置
} object_create_config_t;

// 基础对象操作函数

/**
 * @brief 创建基础对象
 * @param config 创建配置
 * @return 创建的对象指针，失败返回NULL
 */
base_object_t* base_object_create(const object_create_config_t* config);

/**
 * @brief 销毁基础对象
 * @param self 对象指针
 */
void base_object_destroy(base_object_t* self);

/**
 * @brief 增加对象引用计数
 * @param self 对象指针
 * @return 新的引用计数
 */
uint32_t base_object_retain(base_object_t* self);

/**
 * @brief 减少对象引用计数
 * @param self 对象指针
 * @return 新的引用计数，为0时对象被销毁
 */
uint32_t base_object_release(base_object_t* self);

/**
 * @brief 获取对象引用计数
 * @param self 对象指针
 * @return 引用计数
 */
uint32_t base_object_get_ref_count(const base_object_t* self);

/**
 * @brief 验证对象有效性
 * @param self 对象指针
 * @param expected_type 期望的对象类型，OBJ_TYPE_UNKNOWN表示不检查类型
 * @return 验证结果
 */
system_result_e base_object_validate(const base_object_t* self, object_type_e expected_type);

/**
 * @brief 获取对象类型
 * @param self 对象指针
 * @return 对象类型
 */
object_type_e base_object_get_type(const base_object_t* self);

/**
 * @brief 获取对象状态
 * @param self 对象指针
 * @return 对象状态
 */
system_state_e base_object_get_state(const base_object_t* self);

/**
 * @brief 设置对象状态
 * @param self 对象指针
 * @param state 新状态
 * @return 操作结果
 */
system_result_e base_object_set_state(base_object_t* self, system_state_e state);

/**
 * @brief 获取对象私有数据
 * @param self 对象指针
 * @return 私有数据指针
 */
void* base_object_get_private_data(const base_object_t* self);

/**
 * @brief 更新对象最后访问时间
 * @param self 对象指针
 */
void base_object_touch(base_object_t* self);

/**
 * @brief 对象类型转换（安全转换）
 * @param self 对象指针
 * @param target_type 目标类型
 * @return 转换后的指针，失败返回NULL
 */
void* base_object_cast(const base_object_t* self, object_type_e target_type);

// 对象管理器结构
typedef struct {
    base_object_t** objects;        // 对象数组
    uint32_t capacity;              // 容量
    uint32_t count;                 // 当前对象数
    uint32_t next_id;               // 下一个对象ID
} object_manager_t;

/**
 * @brief 初始化对象管理器
 * @param manager 管理器指针
 * @param capacity 最大对象数量
 * @return 操作结果
 */
system_result_e object_manager_init(object_manager_t* manager, uint32_t capacity);

/**
 * @brief 反初始化对象管理器
 * @param manager 管理器指针
 */
void object_manager_deinit(object_manager_t* manager);

/**
 * @brief 注册对象到管理器
 * @param manager 管理器指针
 * @param obj 对象指针
 * @return 对象ID，失败返回0
 */
uint32_t object_manager_register(object_manager_t* manager, base_object_t* obj);

/**
 * @brief 从管理器注销对象
 * @param manager 管理器指针
 * @param obj_id 对象ID
 * @return 操作结果
 */
system_result_e object_manager_unregister(object_manager_t* manager, uint32_t obj_id);

/**
 * @brief 根据ID查找对象
 * @param manager 管理器指针
 * @param obj_id 对象ID
 * @return 对象指针，未找到返回NULL
 */
base_object_t* object_manager_find_by_id(const object_manager_t* manager, uint32_t obj_id);

/**
 * @brief 根据类型查找对象
 * @param manager 管理器指针
 * @param type 对象类型
 * @param results 结果数组
 * @param max_results 最大结果数
 * @return 找到的对象数量
 */
uint32_t object_manager_find_by_type(const object_manager_t* manager, 
                                     object_type_e type,
                                     base_object_t** results, 
                                     uint32_t max_results);

/**
 * @brief 获取管理器统计信息
 * @param manager 管理器指针
 * @param stats 统计信息输出
 * @return 操作结果
 */
system_result_e object_manager_get_stats(const object_manager_t* manager, system_stats_t* stats);

// 便利宏定义

// 定义派生对象结构的宏
#define DEFINE_DERIVED_OBJECT(type_name, private_data_type) \
    typedef struct { \
        base_object_t base; \
        private_data_type private_data; \
    } type_name##_t

// 获取派生对象私有数据的宏
#define GET_PRIVATE_DATA(obj, type) ((type*)base_object_get_private_data((base_object_t*)(obj)))

// 安全类型转换宏
#define SAFE_CAST(obj, target_type, type_enum) \
    ((target_type*)base_object_cast((const base_object_t*)(obj), type_enum))

// 对象验证宏
#define VALIDATE_OBJECT(obj, type_enum) \
    (base_object_validate((const base_object_t*)(obj), type_enum) == SYS_OK)

// 虚函数调用宏
#define CALL_VIRTUAL(obj, method, ...) \
    do { \
        if ((obj) && (obj)->base.vtable && (obj)->base.vtable->method) { \
            (obj)->base.vtable->method((base_object_t*)(obj), ##__VA_ARGS__); \
        } \
    } while(0)

// 虚函数调用并返回结果的宏
#define CALL_VIRTUAL_RETURN(obj, method, default_return, ...) \
    (((obj) && (obj)->base.vtable && (obj)->base.vtable->method) ? \
     (obj)->base.vtable->method((base_object_t*)(obj), ##__VA_ARGS__) : (default_return))

#ifdef __cplusplus
}
#endif

#endif // OBJECT_BASE_H
