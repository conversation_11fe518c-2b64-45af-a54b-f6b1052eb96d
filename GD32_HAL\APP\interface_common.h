#ifndef INTERFACE_COMMON_H
#define INTERFACE_COMMON_H

#ifdef __cplusplus
extern "C" {
#endif

#include "system_types.h"
#include "object_base.h"

// 通用接口标识符
typedef enum {
    INTERFACE_UNKNOWN = 0,
    INTERFACE_INITIALIZABLE = 1,        // 可初始化接口
    INTERFACE_CONFIGURABLE = 2,         // 可配置接口
    INTERFACE_OBSERVABLE = 3,           // 可观察接口
    INTERFACE_SERIALIZABLE = 4,         // 可序列化接口
    INTERFACE_CLONEABLE = 5,            // 可克隆接口
    INTERFACE_COMPARABLE = 6,           // 可比较接口
    INTERFACE_ITERABLE = 7,             // 可迭代接口
    INTERFACE_DISPOSABLE = 8,           // 可释放接口
    INTERFACE_LOCKABLE = 9,             // 可锁定接口
    INTERFACE_CACHEABLE = 10,           // 可缓存接口
    INTERFACE_MAX = 0xFF
} interface_type_e;

// 接口描述符
typedef struct {
    interface_type_e type;              // 接口类型
    const char* name;                   // 接口名称
    uint32_t version;                   // 接口版本
    void* vtable;                       // 虚函数表
} interface_descriptor_t;

// 可初始化接口
typedef struct {
    system_result_e (*init)(void* self, void* config);
    system_result_e (*deinit)(void* self);
    bool (*is_initialized)(const void* self);
} initializable_interface_t;

// 可配置接口
typedef struct {
    system_result_e (*set_config)(void* self, const void* config);
    system_result_e (*get_config)(const void* self, void* config);
    system_result_e (*validate_config)(const void* config);
    system_result_e (*reset_config)(void* self);
} configurable_interface_t;

// 观察者接口
typedef struct {
    system_result_e (*attach)(void* self, void* observer);
    system_result_e (*detach)(void* self, void* observer);
    system_result_e (*notify)(void* self, const system_event_t* event);
    uint32_t (*get_observer_count)(const void* self);
} observable_interface_t;

// 可序列化接口
typedef struct {
    system_result_e (*serialize)(const void* self, uint8_t* buffer, uint32_t* size);
    system_result_e (*deserialize)(void* self, const uint8_t* buffer, uint32_t size);
    uint32_t (*get_serialized_size)(const void* self);
    uint32_t (*get_version)(void);
} serializable_interface_t;

// 可克隆接口
typedef struct {
    void* (*clone)(const void* self);
    void* (*deep_clone)(const void* self);
    system_result_e (*copy_to)(const void* self, void* dest);
} cloneable_interface_t;

// 可比较接口
typedef struct {
    int (*compare)(const void* self, const void* other);
    bool (*equals)(const void* self, const void* other);
    uint32_t (*hash_code)(const void* self);
} comparable_interface_t;

// 迭代器接口
typedef struct {
    bool (*has_next)(const void* iterator);
    void* (*next)(void* iterator);
    void (*reset)(void* iterator);
    void (*destroy)(void* iterator);
} iterator_interface_t;

// 可迭代接口
typedef struct {
    void* (*get_iterator)(const void* self);
    uint32_t (*get_count)(const void* self);
    bool (*is_empty)(const void* self);
} iterable_interface_t;

// 可释放接口
typedef struct {
    void (*dispose)(void* self);
    bool (*is_disposed)(const void* self);
    system_result_e (*add_dispose_callback)(void* self, system_callback_t callback, void* context);
} disposable_interface_t;

// 可锁定接口
typedef struct {
    system_result_e (*lock)(void* self, uint32_t timeout_ms);
    system_result_e (*unlock)(void* self);
    system_result_e (*try_lock)(void* self);
    bool (*is_locked)(const void* self);
} lockable_interface_t;

// 可缓存接口
typedef struct {
    system_result_e (*cache_put)(void* self, const void* key, const void* value);
    system_result_e (*cache_get)(const void* self, const void* key, void** value);
    system_result_e (*cache_remove)(void* self, const void* key);
    system_result_e (*cache_clear)(void* self);
    uint32_t (*cache_size)(const void* self);
} cacheable_interface_t;

// 接口查询函数
typedef void* (*query_interface_func_t)(void* self, interface_type_e type);

// 接口支持检查函数
typedef bool (*supports_interface_func_t)(const void* self, interface_type_e type);

/**
 * @brief 查询对象接口
 * @param obj 对象指针
 * @param type 接口类型
 * @return 接口指针，不支持返回NULL
 */
void* query_interface(void* obj, interface_type_e type);

/**
 * @brief 检查对象是否支持接口
 * @param obj 对象指针
 * @param type 接口类型
 * @return 是否支持
 */
bool supports_interface(const void* obj, interface_type_e type);

/**
 * @brief 注册接口到对象
 * @param obj 对象指针
 * @param descriptor 接口描述符
 * @return 操作结果
 */
system_result_e register_interface(void* obj, const interface_descriptor_t* descriptor);

/**
 * @brief 注销对象接口
 * @param obj 对象指针
 * @param type 接口类型
 * @return 操作结果
 */
system_result_e unregister_interface(void* obj, interface_type_e type);

// 接口便利宏

// 接口查询宏
#define QUERY_INTERFACE(obj, type, interface_type) \
    ((interface_type*)query_interface((obj), (type)))

// 接口支持检查宏
#define SUPPORTS_INTERFACE(obj, type) \
    supports_interface((obj), (type))

// 安全接口调用宏
#define CALL_INTERFACE(obj, type, interface_type, method, ...) \
    do { \
        interface_type* _iface = QUERY_INTERFACE(obj, type, interface_type); \
        if (_iface && _iface->method) { \
            _iface->method((obj), ##__VA_ARGS__); \
        } \
    } while(0)

// 安全接口调用并返回结果宏
#define CALL_INTERFACE_RETURN(obj, type, interface_type, method, default_return, ...) \
    ({ \
        interface_type* _iface = QUERY_INTERFACE(obj, type, interface_type); \
        (_iface && _iface->method) ? \
            _iface->method((obj), ##__VA_ARGS__) : (default_return); \
    })

// 初始化接口宏
#define INIT_OBJECT(obj, config) \
    CALL_INTERFACE_RETURN(obj, INTERFACE_INITIALIZABLE, initializable_interface_t, init, SYS_NOT_SUPPORTED, config)

#define DEINIT_OBJECT(obj) \
    CALL_INTERFACE_RETURN(obj, INTERFACE_INITIALIZABLE, initializable_interface_t, deinit, SYS_NOT_SUPPORTED)

#define IS_INITIALIZED(obj) \
    CALL_INTERFACE_RETURN(obj, INTERFACE_INITIALIZABLE, initializable_interface_t, is_initialized, false)

// 配置接口宏
#define SET_CONFIG(obj, config) \
    CALL_INTERFACE_RETURN(obj, INTERFACE_CONFIGURABLE, configurable_interface_t, set_config, SYS_NOT_SUPPORTED, config)

#define GET_CONFIG(obj, config) \
    CALL_INTERFACE_RETURN(obj, INTERFACE_CONFIGURABLE, configurable_interface_t, get_config, SYS_NOT_SUPPORTED, config)

// 观察者接口宏
#define ATTACH_OBSERVER(obj, observer) \
    CALL_INTERFACE_RETURN(obj, INTERFACE_OBSERVABLE, observable_interface_t, attach, SYS_NOT_SUPPORTED, observer)

#define DETACH_OBSERVER(obj, observer) \
    CALL_INTERFACE_RETURN(obj, INTERFACE_OBSERVABLE, observable_interface_t, detach, SYS_NOT_SUPPORTED, observer)

#define NOTIFY_OBSERVERS(obj, event) \
    CALL_INTERFACE_RETURN(obj, INTERFACE_OBSERVABLE, observable_interface_t, notify, SYS_NOT_SUPPORTED, event)

// 序列化接口宏
#define SERIALIZE_OBJECT(obj, buffer, size) \
    CALL_INTERFACE_RETURN(obj, INTERFACE_SERIALIZABLE, serializable_interface_t, serialize, SYS_NOT_SUPPORTED, buffer, size)

#define DESERIALIZE_OBJECT(obj, buffer, size) \
    CALL_INTERFACE_RETURN(obj, INTERFACE_SERIALIZABLE, serializable_interface_t, deserialize, SYS_NOT_SUPPORTED, buffer, size)

// 克隆接口宏
#define CLONE_OBJECT(obj) \
    CALL_INTERFACE_RETURN(obj, INTERFACE_CLONEABLE, cloneable_interface_t, clone, NULL)

#define DEEP_CLONE_OBJECT(obj) \
    CALL_INTERFACE_RETURN(obj, INTERFACE_CLONEABLE, cloneable_interface_t, deep_clone, NULL)

// 比较接口宏
#define COMPARE_OBJECTS(obj1, obj2) \
    CALL_INTERFACE_RETURN(obj1, INTERFACE_COMPARABLE, comparable_interface_t, compare, 0, obj2)

#define OBJECTS_EQUAL(obj1, obj2) \
    CALL_INTERFACE_RETURN(obj1, INTERFACE_COMPARABLE, comparable_interface_t, equals, false, obj2)

// 迭代器接口宏
#define GET_ITERATOR(obj) \
    CALL_INTERFACE_RETURN(obj, INTERFACE_ITERABLE, iterable_interface_t, get_iterator, NULL)

#define GET_COUNT(obj) \
    CALL_INTERFACE_RETURN(obj, INTERFACE_ITERABLE, iterable_interface_t, get_count, 0)

#define IS_EMPTY(obj) \
    CALL_INTERFACE_RETURN(obj, INTERFACE_ITERABLE, iterable_interface_t, is_empty, true)

// 释放接口宏
#define DISPOSE_OBJECT(obj) \
    CALL_INTERFACE(obj, INTERFACE_DISPOSABLE, disposable_interface_t, dispose)

#define IS_DISPOSED(obj) \
    CALL_INTERFACE_RETURN(obj, INTERFACE_DISPOSABLE, disposable_interface_t, is_disposed, true)

// 锁定接口宏
#define LOCK_OBJECT(obj, timeout) \
    CALL_INTERFACE_RETURN(obj, INTERFACE_LOCKABLE, lockable_interface_t, lock, SYS_NOT_SUPPORTED, timeout)

#define UNLOCK_OBJECT(obj) \
    CALL_INTERFACE_RETURN(obj, INTERFACE_LOCKABLE, lockable_interface_t, unlock, SYS_NOT_SUPPORTED)

#define TRY_LOCK_OBJECT(obj) \
    CALL_INTERFACE_RETURN(obj, INTERFACE_LOCKABLE, lockable_interface_t, try_lock, SYS_NOT_SUPPORTED)

#ifdef __cplusplus
}
#endif

#endif // INTERFACE_COMMON_H
