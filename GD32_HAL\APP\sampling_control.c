#include "sampling_control.h"
#include "stddef.h"
#include "config_manager.h" // ���ù���ϵͳ
#include "adc_app.h"        // ADCӦ��ģ��

// Copyright (c) 2024 �״׵��ӹ�����. All rights reserved.

// ȫ�ֲ�������ʵ��
static sampling_control_t g_sampling_control = {0};
static uint8_t g_sampling_initialized = 0;

// LED��˸���ڶ��� (1�� = 1000ms)
#define LED_BLINK_PERIOD_MS 1000

// ��ʼ����������ϵͳ
sampling_status_t sampling_init(void)
{
    if (g_sampling_initialized)
    {
        return SAMPLING_OK;
    }

    // ��ʼ�����ù���ϵͳ
    config_init();

    // ��ʼ���������ƽṹ��
    g_sampling_control.state = SAMPLING_IDLE;
    g_sampling_control.cycle = config_get_sampling_cycle(); // ������ϵͳ��������
    g_sampling_control.last_sample_time = 0;
    g_sampling_control.led_blink_time = 0;
    g_sampling_control.led_blink_state = 0;

    g_sampling_initialized = 1;
    return SAMPLING_OK;
}

// ��������
sampling_status_t sampling_start(void)
{
    if (!g_sampling_initialized)
        return SAMPLING_ERROR;

    g_sampling_control.state = SAMPLING_ACTIVE;
    g_sampling_control.last_sample_time = HAL_GetTick();
    g_sampling_control.led_blink_time = HAL_GetTick();
    g_sampling_control.led_blink_state = 0;

    return SAMPLING_OK;
}

// ֹͣ����
sampling_status_t sampling_stop(void)
{
    if (!g_sampling_initialized)
        return SAMPLING_ERROR;

    g_sampling_control.state = SAMPLING_IDLE;
    g_sampling_control.led_blink_state = 0; // LEDϨ��

    return SAMPLING_OK;
}

// ���ò�������
sampling_status_t sampling_set_cycle(sampling_cycle_t cycle)
{
    if (!g_sampling_initialized)
        return SAMPLING_ERROR;

    // ��֤���ڲ���
    if (cycle != CYCLE_5S && cycle != CYCLE_10S && cycle != CYCLE_15S)
    {
        return SAMPLING_INVALID;
    }

    // ���±�����������
    g_sampling_control.cycle = cycle;

    // ���浽���ù���ϵͳ��д��Flash
    if (config_set_sampling_cycle(cycle) == CONFIG_OK)
    {
        config_save_to_flash();
    }

    return SAMPLING_OK;
}

// ��ȡ����״̬
sampling_state_t sampling_get_state(void)
{
    if (!g_sampling_initialized)
        return SAMPLING_IDLE;
    return g_sampling_control.state;
}

// ��ȡ��������
sampling_cycle_t sampling_get_cycle(void)
{
    if (!g_sampling_initialized)
        return CYCLE_5S;
    return g_sampling_control.cycle;
}

// ����Ƿ�Ӧ�ò���
uint8_t sampling_should_sample(void)
{
    if (!g_sampling_initialized || g_sampling_control.state != SAMPLING_ACTIVE)
    {
        return 0;
    }

    uint32_t current_time = HAL_GetTick();
    uint32_t elapsed_time = current_time - g_sampling_control.last_sample_time;
    uint32_t cycle_ms = g_sampling_control.cycle * 1000; // ת��Ϊ����

    return (elapsed_time >= cycle_ms) ? 1 : 0;
}

// ����LED��˸״̬
void sampling_update_led_blink(void)
{
    if (!g_sampling_initialized || g_sampling_control.state != SAMPLING_ACTIVE)
    {
        g_sampling_control.led_blink_state = 0; // ����ֹͣʱLEDϨ��
        return;
    }

    uint32_t current_time = HAL_GetTick();
    uint32_t elapsed_time = current_time - g_sampling_control.led_blink_time;

    if (elapsed_time >= LED_BLINK_PERIOD_MS)
    {
        g_sampling_control.led_blink_state ^= 1; // ��תLED״̬
        g_sampling_control.led_blink_time = current_time;
    }
}

// ��ȡ��ǰ��ѹֵ
float sampling_get_voltage(void)
{
    extern __IO float voltage; // ����adc_app.c��ȫ�ֵ�ѹ����
    config_params_t config_params;

    // ��ȡ���ò���
    if (config_get_params(&config_params) != CONFIG_OK)
    {
        return voltage; // ���û�ȡʧ��ʱ����ԭʼ��ѹֵ
    }

    // ʹ��ratio�������е�ѹ����
    return voltage * config_params.ratio;
}

// ����Ƿ���
uint8_t sampling_check_overlimit(void)
{
    config_params_t config_params;

    // ��ȡ���ò���
    if (config_get_params(&config_params) != CONFIG_OK)
    {
        return 0; // ���û�ȡʧ��ʱ��Ϊδ����
    }

    // ��ȡ��ǰ��ѹֵ
    float current_voltage = sampling_get_voltage();

    // ����Ƿ񳬹�limit��ֵ
    return (current_voltage > config_params.limit) ? 1 : 0;
}

// ����������
void sampling_task(void)
{
    if (!g_sampling_initialized)
        return;

    // ����LED��˸״̬
    sampling_update_led_blink();

    // ������ڲ���״̬������Ƿ���Ҫ���в���
    if (g_sampling_control.state == SAMPLING_ACTIVE)
    {
        if (sampling_should_sample())
        {
            // ���²���ʱ���
            g_sampling_control.last_sample_time = HAL_GetTick();

            // ��ȡ��ǰ��ѹֵ
            float current_voltage = sampling_get_voltage();

            // ����Ƿ���
            uint8_t is_overlimit = sampling_check_overlimit();

            // ����������Ӳ������ݵĴ����߼�
            // ʵ�ʵ�������ڴ�������ģ����ʵ��
            (void)current_voltage; // ����δʹ�ñ�������
            (void)is_overlimit;    // ����δʹ�ñ�������
        }
    }
}

// ��ȡLED��˸״̬
uint8_t sampling_get_led_blink_state(void)
{
    if (!g_sampling_initialized)
        return 0;
    return g_sampling_control.led_blink_state;
}
