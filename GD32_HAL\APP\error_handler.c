#include "error_handler.h"
#include "memory_manager.h"
#include <stdio.h>
#include <stdarg.h>
#include <string.h>

// 全局错误处理器实例
error_handler_t* g_error_handler = NULL;

// 错误处理器虚函数表
static void error_handler_destroy_impl(base_object_t* self);
static object_type_e error_handler_get_type_impl(const base_object_t* self);
static uint32_t error_handler_get_size_impl(const base_object_t* self);
static system_result_e error_handler_init_impl(base_object_t* self, void* config);
static system_result_e error_handler_deinit_impl(base_object_t* self);

static const object_vtable_t error_handler_vtable = {
    .destroy = error_handler_destroy_impl,
    .get_type = error_handler_get_type_impl,
    .get_size = error_handler_get_size_impl,
    .init = error_handler_init_impl,
    .deinit = error_handler_deinit_impl,
    .clone = NULL,
    .compare = NULL,
    .serialize = NULL,
    .deserialize = NULL,
    .to_string = NULL
};

// 系统错误码字符串映射
static const char* error_strings[] = {
    "SYS_OK",
    "SYS_ERROR", 
    "SYS_INVALID_PARAM",
    "SYS_NO_MEMORY",
    "SYS_TIMEOUT",
    "SYS_BUSY",
    "SYS_NOT_SUPPORTED",
    "SYS_NOT_INITIALIZED",
    "SYS_ALREADY_INITIALIZED",
    "SYS_INVALID_STATE",
    "SYS_BUFFER_FULL",
    "SYS_BUFFER_EMPTY",
    "SYS_CRC_ERROR",
    "SYS_IO_ERROR",
    "SYS_PERMISSION_DENIED",
    "SYS_RESOURCE_UNAVAILABLE"
};

// 获取系统时间的弱函数实现
__attribute__((weak)) system_time_t get_system_time(void) {
    // 默认实现，实际项目中应该使用HAL_GetTick()或其他时间函数
    static uint32_t tick = 0;
    return ++tick;
}

// 虚函数实现
static void error_handler_destroy_impl(base_object_t* self) {
    if (self) {
        error_handler_t* handler = (error_handler_t*)self;
        // 清理私有数据
        memset(&handler->private_data, 0, sizeof(handler->private_data));
        // 释放对象内存
        sys_free(handler);
    }
}

static object_type_e error_handler_get_type_impl(const base_object_t* self) {
    (void)self;
    return OBJ_TYPE_ERROR_HANDLER;
}

static uint32_t error_handler_get_size_impl(const base_object_t* self) {
    (void)self;
    return sizeof(error_handler_t);
}

static system_result_e error_handler_init_impl(base_object_t* self, void* config) {
    if (!self || !config) {
        return SYS_INVALID_PARAM;
    }
    
    error_handler_t* handler = (error_handler_t*)self;
    error_handler_config_t* cfg = (error_handler_config_t*)config;
    
    // 初始化私有数据
    handler->private_data.log_func = cfg->log_func;
    handler->private_data.error_callback = cfg->error_callback;
    handler->private_data.min_log_level = cfg->min_log_level;
    handler->private_data.panic_on_critical = cfg->panic_on_critical;
    handler->private_data.error_count = 0;
    handler->private_data.warning_count = 0;
    memset(handler->private_data.last_error_msg, 0, sizeof(handler->private_data.last_error_msg));
    
    return SYS_OK;
}

static system_result_e error_handler_deinit_impl(base_object_t* self) {
    if (!self) {
        return SYS_INVALID_PARAM;
    }
    
    error_handler_t* handler = (error_handler_t*)self;
    memset(&handler->private_data, 0, sizeof(handler->private_data));
    
    return SYS_OK;
}

// 公共函数实现
error_handler_t* error_handler_create(const error_handler_config_t* config) {
    if (!config) {
        return NULL;
    }
    
    // 创建对象配置
    object_create_config_t obj_config = {
        .type = OBJ_TYPE_ERROR_HANDLER,
        .private_data_size = 0, // 使用内嵌的private_data
        .vtable = &error_handler_vtable,
        .init_config = (void*)config
    };
    
    // 分配内存
    error_handler_t* handler = (error_handler_t*)sys_malloc(sizeof(error_handler_t));
    if (!handler) {
        return NULL;
    }
    
    // 初始化基础对象
    handler->base.magic = SYSTEM_MAGIC_BASE | OBJ_TYPE_ERROR_HANDLER;
    handler->base.ref_count = 1;
    handler->base.state = STATE_INITIALIZING;
    handler->base.vtable = &error_handler_vtable;
    handler->base.private_data = &handler->private_data;
    handler->base.created_time = get_system_time();
    handler->base.last_access_time = handler->base.created_time;
    
    // 调用初始化函数
    if (error_handler_init_impl(&handler->base, (void*)config) != SYS_OK) {
        sys_free(handler);
        return NULL;
    }
    
    handler->base.state = STATE_READY;
    return handler;
}

void error_handler_destroy(error_handler_t* handler) {
    if (handler) {
        base_object_destroy(&handler->base);
    }
}

system_result_e error_handler_global_init(const error_handler_config_t* config) {
    if (g_error_handler) {
        return SYS_ALREADY_INITIALIZED;
    }
    
    g_error_handler = error_handler_create(config);
    return g_error_handler ? SYS_OK : SYS_ERROR;
}

void error_handler_global_deinit(void) {
    if (g_error_handler) {
        error_handler_destroy(g_error_handler);
        g_error_handler = NULL;
    }
}

system_result_e error_handler_handle_error(error_handler_t* handler,
                                           const error_info_t* error_info,
                                           error_strategy_e strategy) {
    if (!error_info) {
        return SYS_INVALID_PARAM;
    }
    
    // 使用全局处理器如果未指定
    if (!handler) {
        handler = g_error_handler;
    }
    
    if (!handler) {
        return SYS_NOT_INITIALIZED;
    }
    
    // 更新统计信息
    if (error_info->error_code != SYS_OK) {
        handler->private_data.error_count++;
    }
    
    // 保存最后错误消息
    if (error_info->message) {
        strncpy(handler->private_data.last_error_msg, error_info->message, 
                sizeof(handler->private_data.last_error_msg) - 1);
        handler->private_data.last_error_msg[sizeof(handler->private_data.last_error_msg) - 1] = '\0';
    }
    
    // 根据策略处理错误
    switch (strategy) {
        case ERROR_STRATEGY_IGNORE:
            break;
            
        case ERROR_STRATEGY_LOG:
            if (handler->private_data.log_func) {
                handler->private_data.log_func(LOG_LEVEL_ERROR, "ERROR", 
                    "[%s:%d] %s: %s", 
                    error_info->file ? error_info->file : "unknown",
                    error_info->line,
                    error_info->function ? error_info->function : "unknown",
                    error_info->message ? error_info->message : "no message");
            }
            break;
            
        case ERROR_STRATEGY_CALLBACK:
            if (handler->private_data.error_callback) {
                handler->private_data.error_callback(handler, (void*)error_info);
            }
            break;
            
        case ERROR_STRATEGY_PANIC:
            if (handler->private_data.panic_on_critical) {
                system_panic(error_info->message);
            }
            break;
            
        default:
            break;
    }
    
    return SYS_OK;
}

void error_handler_log(error_handler_t* handler,
                      log_level_e level,
                      const char* file,
                      uint32_t line,
                      const char* func,
                      const char* format, ...) {
    // 使用全局处理器如果未指定
    if (!handler) {
        handler = g_error_handler;
    }
    
    if (!handler || !handler->private_data.log_func) {
        return;
    }
    
    // 检查日志级别
    if (level > handler->private_data.min_log_level) {
        return;
    }
    
    // 格式化消息
    char message[512];
    va_list args;
    va_start(args, format);
    vsnprintf(message, sizeof(message), format, args);
    va_end(args);
    
    // 调用日志函数
    handler->private_data.log_func(level, "APP", "[%s:%d] %s: %s",
        file ? file : "unknown",
        line,
        func ? func : "unknown",
        message);
    
    // 更新统计
    if (level == LOG_LEVEL_ERROR) {
        handler->private_data.error_count++;
    } else if (level == LOG_LEVEL_WARN) {
        handler->private_data.warning_count++;
    }
}

system_result_e error_handler_get_stats(error_handler_t* handler,
                                        uint32_t* error_count,
                                        uint32_t* warning_count) {
    if (!handler) {
        handler = g_error_handler;
    }
    
    if (!handler) {
        return SYS_NOT_INITIALIZED;
    }
    
    if (error_count) {
        *error_count = handler->private_data.error_count;
    }
    
    if (warning_count) {
        *warning_count = handler->private_data.warning_count;
    }
    
    return SYS_OK;
}

system_result_e error_handler_reset_stats(error_handler_t* handler) {
    if (!handler) {
        handler = g_error_handler;
    }
    
    if (!handler) {
        return SYS_NOT_INITIALIZED;
    }
    
    handler->private_data.error_count = 0;
    handler->private_data.warning_count = 0;
    
    return SYS_OK;
}

const char* error_handler_get_last_error(error_handler_t* handler) {
    if (!handler) {
        handler = g_error_handler;
    }
    
    if (!handler) {
        return "Error handler not initialized";
    }
    
    return handler->private_data.last_error_msg;
}

const char* system_result_to_string(system_result_e result) {
    if (result < ARRAY_SIZE(error_strings)) {
        return error_strings[result];
    }
    return "UNKNOWN_ERROR";
}

void system_assert_failed(const char* file, uint32_t line, const char* expr) {
    // 记录断言失败
    if (g_error_handler && g_error_handler->private_data.log_func) {
        g_error_handler->private_data.log_func(LOG_LEVEL_ERROR, "ASSERT", 
            "Assertion failed: %s at %s:%d", expr, file, line);
    }
    
    // 触发系统panic
    system_panic("Assertion failed");
}

void system_panic(const char* message) {
    // 记录panic消息
    if (g_error_handler && g_error_handler->private_data.log_func) {
        g_error_handler->private_data.log_func(LOG_LEVEL_ERROR, "PANIC", 
            "System panic: %s", message ? message : "unknown reason");
    }
    
    // 在实际系统中，这里应该执行系统重启或进入安全模式
    // 这里只是简单的无限循环
    while (1) {
        // 可以在这里添加看门狗喂狗或其他安全措施
    }
}
